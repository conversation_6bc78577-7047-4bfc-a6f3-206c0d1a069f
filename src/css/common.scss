/* 定义全局通用的css变量 */

:root {
  /* 自定义css变量 */
  --bf-base-font-size: 16px;
  --bf-border-size: 2px;
  --bf-text-highlight: #ff811d;
  --bf-btn-info-color: 117, 117, 117; //#757575;
  --bf-btn-default-color: 96, 172, 218; //#60acda;
  --bf-btn-warning-color: 255, 129, 29; //#ff811d;
  --bf-btn-danger-color: 255, 74, 74; //#ff4a4a;
  --bf-btn-primary-color: var(--bf-btn-warning-color);
  --bf-btn-stroke-color: var(--bf-btn-warning-color);
  --bf-btn-stroke-width: 2;
  --bf-btn-fill-color: var(--bf-btn-warning-color);
  --bf-btn-text-color: 255, 255, 255; //#fff
  --bf-btn-corner-size: 6px;
  --bf-form-item-btn-border-color: rgba(148, 204, 232, 1);
  --bf-form-item-btn-hover-border-color: rgba(148, 204, 232, 0.7);

  /* 重写element-plus全局变量 */
  --el-text-color-regular: #fff;
}

.bf-component-size {
  --el-component-size-large: 68px;
  --el-component-size: 50px;
  --el-component-size-small: 38px;
}

// 表单内按钮样式
.bf-form-item-button.el-button {
  border: none;
  border-radius: unset;
  background-color: transparent;
  box-shadow: 0 0 0 var(--bf-border-size) var(--bf-form-item-btn-border-color) inset;

  &:hover {
    background-color: transparent;
    box-shadow: 0 0 0 var(--bf-border-size) var(--bf-form-item-btn-hover-border-color) inset;
  }
}

// El Message 样式重置
.bf-message {
  --bf-message-info-border-color: #9A9A9A;
  --bf-message-success-border-color: #1398E9;
  --bf-message-warning-border-color: #FF801D;
  --bf-message-error-border-color: #FF4E4E;

  background-color: #fff;
  &.bf-message-info {
    border: var(--bf-border-size) solid var(--bf-message-info-border-color);
  }
  &.bf-message-success {
    border: var(--bf-border-size) solid var(--bf-message-success-border-color);
  }
  &.bf-message-warning {
    border: var(--bf-border-size) solid var(--bf-message-warning-border-color);
  }
  &.bf-message-error {
    border: var(--bf-border-size) solid var(--bf-message-error-border-color);
  }

  .el-message__icon {
    width: 30px;
    height: 30px;
    border-radius: 2px;

    &:has(.bf-icon-info) {
      background-color: var(--bf-message-info-border-color);
    }
    &:has(.bf-icon-success) {
      background-color: var(--bf-message-success-border-color);
    }
    &:has(.bf-icon-warning) {
      background-color: var(--bf-message-warning-border-color);
    }
    &:has(.bf-icon-error) {
      background-color: var(--bf-message-error-border-color);
    }


    .bf-icon {
      color: #fff;
    }
  }
}