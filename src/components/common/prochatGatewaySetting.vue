<template>
  <BfDialog
    v-model="prochatGatewayVisible"
    :title="$t('dialog.prochatGatewayConfig')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    class="header-border footer-border prochat-gateway-setting-dialog"
    @close="onClose"
  >
    <el-form ref="formData" :model="formData" :rules="rules" label-width="160px" :validate-on-rule-change="false" class="grid grid-cols-1">
      <el-form-item ref="hostItem" prop="host">
        <template #label>
          <EllipsisText :content="$t('dialog.prochatDomain') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfInput v-model="formData.host" :maxlength="56" style="height: 50px" />
      </el-form-item>
      <el-form-item ref="portItem" prop="port">
        <template #label>
          <EllipsisText :content="$t('dialog.prochatPort') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfInputNumberV2 v-model.number="formData.port" class="!w-full" :min="0x01" :max="0xffff" step-strictly />
      </el-form-item>
      <el-form-item prop="prochatNo">
        <template #label>
          <EllipsisText :content="$t('dialog.prochatNo') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfInput v-model="formData.prochatNo" :maxlength="16" style="height: 50px" />
      </el-form-item>
      <el-form-item prop="userNo">
        <template #label>
          <EllipsisText :content="$t('dialog.prochatUserNo') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfInput v-model="formData.userNo" :maxlength="16" style="height: 50px" />
      </el-form-item>
      <el-form-item prop="password">
        <template #label>
          <EllipsisText :content="$t('dialog.prochatPassword') + ':'" class="text-left" style="line-height: 50px" />
        </template>
        <BfInput v-model="formData.password" type="password" :maxlength="16" style="height: 50px" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="text-center flex justify-center gap-3">
        <BfButton color-type="primary" @click="onConfirm">
          {{ $t('dialog.confirm') }}
        </BfButton>
        <BfButton color-type="info" @click="onReset">
          {{ $t('dialog.reset') }}
        </BfButton>
      </div>
    </template>
  </BfDialog>
</template>

<script>
  import validateRules from '@/utils/validateRules'
  import BfDialog from '@/components/bfDialog/main'
  import BfButton from '@/components/bfButton/main'
  import BfInput from '@/components/bfInput/main'
  import BfInputNumberV2 from '@/components/bfInputNumber/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'

  /**
   * @param prochatNo - 公网网关的号码
   * @param host - 公网网关的 ip 或域名
   * @param port - 公网网关的端口
   * @param userNo - 专网网关(本系统)的号码
   * @param password - 专网网关的密码
   *
   * 所有的配置项存放在 controller.setting 中，controller.sipNo 为 prochatNo
   */
  export const DefaultFormData = {
    prochatNo: '',
    host: '',
    port: 5060,
    userNo: '',
    password: '',
  }

  export default {
    name: 'ProchatGatewaySetting',
    emits: ['update:modelValue', 'update:visible'],
    components: {
      BfDialog,
      BfButton,
      BfInput,
      BfInputNumberV2,
      EllipsisText,
    },
    props: {
      modelValue: {
        type: Object,
        required: true,
      },
      visible: {
        type: Boolean,
        required: true,
      },
    },
    data() {
      return {
        formData: {
          ...DefaultFormData,
        },
      }
    },
    computed: {
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      prochatGatewayVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      rules() {
        return {
          prochatNo: [validateRules.required(), validateRules.maxLen(12)],
          host: [validateRules.required(), validateRules.checkHost()],
          port: [validateRules.required()],
          userNo: [validateRules.required(), validateRules.maxLen(12)],
          password: [validateRules.required(), validateRules.maxLen(8)],
        }
      },
    },
    methods: {
      onClose() {
        this.prochatGatewayVisible = false
      },
      async validate() {
        return await this.$refs.formData.validate().catch(() => false)
      },
      async onConfirm() {
        const valid = await this.validate()
        if (!valid) {
          return
        }

        this.$emit('update:modelValue', this.formData)
        this.onClose()
      },
      onReset() {
        this.formData = {
          ...DefaultFormData,
        }
        this.$nextTick(() => {
          this.$refs.formData?.clearValidate()
        })
      },
    },
    watch: {
      modelValue: {
        handler(val) {
          this.formData = val
        },
        immediate: true,
        deep: true,
      },
    },
  }
</script>

<style lang="scss">
  .el-dialog.prochat-gateway-setting-dialog {
    width: 520px;
  }
</style>
