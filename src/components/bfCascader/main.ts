import { defineComponent, h, computed, ref } from 'vue'
import { ElCascader } from 'element-plus'
import 'element-plus/es/components/cascader/style/css'
import './main.scss'

export default defineComponent({
  name: 'BfCascader',
  props: {
    modelValue: {
      type: [String, Number, Object, Array],
      default: () => [],
    },
    ...ElCascader.props,
  },
  emits: ['update:modelValue', 'change', 'expand-change', 'blur', 'focus', 'visible-change', 'remove-tag'],
  setup(props: InstanceType<typeof ElCascader>['$props'], { emit, slots, expose, attrs }) {
    const cascaderVal = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      },
    })

    const cascaderProps = computed(() => {
      return {
        ...attrs,
        ...props,
        class: ['bf-cascader', attrs.class].filter(Boolean).join(' '),
        modelValue: cascaderVal.value,
        'onUpdate:modelValue': val => {
          cascaderVal.value = val
        },
        onChange: val => {
          emit('change', val)
        },
        onExpandChange: val => {
          emit('expand-change', val)
        },
        onBlur: event => {
          emit('blur', event)
        },
        onFocus: event => {
          emit('focus', event)
        },
        onVisibleChange: visible => {
          emit('visible-change', visible)
        },
        onRemoveTag: tag => {
          emit('remove-tag', tag)
        },
      }
    })

    const suffixIcon = computed(() => {
      return h('span', {
        class: 'iconfont bfdx-xiala text-[20px]',
      })
    })

    const cascaderRef = ref<InstanceType<typeof ElCascader>>()
    expose({
      cascaderRef,
      getCheckedNodes: (leafOnly = false) => cascaderRef.value?.getCheckedNodes(leafOnly),
    })

    // 使用 h 函数渲染 ElCascader
    return () =>
      h(
        ElCascader,
        {
          ...cascaderProps.value,
          popperClass: 'bf-cascader-popper',
          ref: cascaderRef,
          suffixIcon,
        },
        slots
      )
  },
})
